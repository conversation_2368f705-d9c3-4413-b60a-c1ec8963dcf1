{"name": "happi-chocolate-demo", "version": "1.0.0", "description": "Happi 巧克力演示站点 - 纯前端项目", "main": "index.html", "scripts": {"serve": "live-server --port=3000 --open=/index.html", "start": "npm run serve", "dev": "live-server --port=3000 --open=/index.html --watch=assets/css,assets/js", "build": "echo '项目已经是静态文件，无需构建步骤'", "preview": "live-server --port=8080 --open=/index.html"}, "keywords": ["chocolate", "frontend", "static-site", "demo"], "author": "", "license": "MIT", "devDependencies": {"live-server": "^1.2.2"}, "engines": {"node": ">=14.0.0"}}