# Happi 巧克力演示站点

这是一个纯前端的静态网站项目，展示了 Happi 巧克力品牌的演示页面。

## 项目结构

```
chocolate/
├── index.html          # 首页
├── products.html       # 商店页面
├── story.html         # 关于我们页面
├── benefits.html      # 优势页面
├── subscribe.html     # 订阅页面
├── assets/
│   ├── css/
│   │   └── styles.css # 样式文件
│   └── js/
│       └── app.js     # JavaScript 文件
├── package.json       # 项目配置
└── README.md         # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run serve
```

或者使用：

```bash
npm start
```

这将启动一个本地开发服务器，默认在 `http://localhost:3000` 打开网站。

### 3. 其他可用命令

- `npm run dev` - 启动开发服务器并监听文件变化
- `npm run preview` - 在端口 8080 预览项目
- `npm run build` - 显示构建信息（静态项目无需构建）

## 功能特性

- 📱 响应式设计
- 🎨 现代化 UI 界面
- 🚀 纯前端静态网站
- 🔥 热重载开发体验
- 📦 轻量级项目结构

## 技术栈

- HTML5
- CSS3 (包含现代布局和动画)
- Vanilla JavaScript
- Live Server (开发服务器)

## 浏览器支持

支持所有现代浏览器：
- Chrome (推荐)
- Firefox
- Safari
- Edge

## 开发说明

这是一个纯展示性的静态网站，所有的表单提交和交互都是模拟的，没有后端逻辑。

## 许可证

MIT License
